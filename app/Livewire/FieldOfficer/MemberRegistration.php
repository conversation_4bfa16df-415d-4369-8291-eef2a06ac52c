<?php

namespace App\Livewire\FieldOfficer;

use Livewire\Component;
use App\Models\Member;
use App\Models\Branch;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Livewire\WithFileUploads;
use Livewire\Attributes\Validate;

class MemberRegistration extends Component
{
    use WithFileUploads;

    // Form properties
    #[Validate('required|string|max:255')]
    public $name = '';

    #[Validate('required|string|max:255')]
    public $father_or_husband_name = '';

    #[Validate('required|string|max:255')]
    public $mother_name = '';

    #[Validate('required|date|before:today')]
    public $date_of_birth = '';

    #[Validate('required|string|max:20|unique:members,nid_number')]
    public $nid_number = '';

    #[Validate('nullable|string|max:50')]
    public $religion = '';

    #[Validate('nullable|string|max:10')]
    public $blood_group = '';

    #[Validate('required|string|max:15|unique:members,phone_number')]
    public $phone_number = '';

    #[Validate('required|string|max:500')]
    public $present_address = '';

    #[Validate('required|string|max:500')]
    public $permanent_address = '';

    public $same_as_present = false;

    #[Validate('nullable|image|max:2048')] // 2MB Max
    public $photo;

    #[Validate('required|string|max:100')]
    public $occupation = '';

    #[Validate('nullable|exists:members,id')]
    public $reference_id = '';

    #[Validate('required|exists:branches,id')]
    public $branch_id;

    // Component state
    public $photo_preview;
    public $generated_member_id;
    public ?\Illuminate\Support\Collection $branches = null;
     public $references = [];


    public function mount()
    {
        $user = Auth::user();
        $this->branches = Branch::where('is_active', true)->orderBy('name')->get();
        
        if ($user->role !== 'admin' && $user->branch_id) {
            $assignedBranch = $this->branches->firstWhere('id', $user->branch_id);
            if ($assignedBranch) {
                $this->branch_id = $user->branch_id;
                $this->generateMemberId();
            } else {
                \Illuminate\Support\Facades\Log::warning('Field officer ' . $user->id . ' has an invalid or inactive branch_id: ' . $user->branch_id);
                // Optionally, you could add a session flash message here to inform the user
            }
        }
        
        $this->loadReferences();
    }

    public function updatedPhoto()
    {
        $this->validate(['photo' => 'nullable|image|max:2048']);
        if ($this->photo) {
            $this->photo_preview = $this->photo->temporaryUrl();
        }
    }
    
    public function removePhoto()
    {
        $this->photo = null;
        $this->photo_preview = null;
    }

    public function toggleSameAddress()
    {
        $this->permanent_address = $this->same_as_present ? $this->present_address : '';
    }
    
    public function updatedPresentAddress()
    {
        if ($this->same_as_present) {
            $this->permanent_address = $this->present_address;
        }
    }

    public function updatedBranchId()
    {
        $this->generateMemberId();
    }

    public function generateMemberId()
    {
        if (!$this->branch_id) {
            $this->generated_member_id = null;
            return;
        }

        try {
            $branchCode = str_pad($this->branch_id, 3, '0', STR_PAD_LEFT);
            
            // Get the actual count of members in this branch to ensure uniqueness
            $memberCount = Member::where('branch_id', $this->branch_id)->count();
            
            // Try up to 10 times to generate a unique ID
            for ($attempt = 0; $attempt < 10; $attempt++) {
                $nextId = $memberCount + 1 + $attempt;
                $memberCode = str_pad($nextId, 6, '0', STR_PAD_LEFT);
                $proposedId = "M{$branchCode}{$memberCode}";
                
                // Check if this ID already exists
                if (!Member::where('member_id', $proposedId)->exists()) {
                    $this->generated_member_id = $proposedId;
                    return;
                }
            }
            
            // If we couldn't generate a unique ID after 10 attempts, use timestamp
            $timestamp = now()->format('His'); // HHMMSS
            $memberCode = str_pad($memberCount + 1, 3, '0', STR_PAD_LEFT) . $timestamp;
            $this->generated_member_id = "M{$branchCode}{$memberCode}";
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to generate member ID: ' . $e->getMessage());
            $this->generated_member_id = null;
        }
    }

    public function loadReferences()
    {
        $this->references = Member::where('is_active', true)->orderBy('name')->get(['id', 'name', 'member_id']);
    }

    public function createMember()
    {
        $this->validate();

        try {
            \Illuminate\Support\Facades\DB::beginTransaction();
            
            // Ensure we have a valid member ID
            $this->generateMemberId();
            
            if (!$this->generated_member_id) {
                throw new \Exception('Failed to generate member ID. Please check branch selection.');
            }

            // Verify branch exists and is active
            $branch = \App\Models\Branch::where('id', $this->branch_id)
                ->where('is_active', true)
                ->first();
            
            if (!$branch) {
                throw new \Exception('Invalid or inactive branch selected.');
            }

            $photoPath = null;
            if ($this->photo) {
                try {
                    // Ensure storage directory exists
                    if (!\Illuminate\Support\Facades\Storage::disk('public')->exists('member_photos')) {
                        \Illuminate\Support\Facades\Storage::disk('public')->makeDirectory('member_photos');
                    }
                    $photoPath = $this->photo->store('member_photos', 'public');
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::warning('Photo upload failed: ' . $e->getMessage());
                    // Continue without photo
                }
            }

            // Double-check for duplicate member ID
            $existingMember = Member::where('member_id', $this->generated_member_id)->first();
            if ($existingMember) {
                $this->generateMemberId(); // Try generating a new one
                if (Member::where('member_id', $this->generated_member_id)->exists()) {
                    throw new \Exception('Unable to generate unique member ID. Please try again.');
                }
            }

            $member = Member::create([
                'member_id' => $this->generated_member_id,
                'name' => trim($this->name),
                'father_or_husband_name' => trim($this->father_or_husband_name),
                'mother_name' => trim($this->mother_name),
                'date_of_birth' => $this->date_of_birth,
                'nid_number' => trim($this->nid_number),
                'religion' => $this->religion ?: null,
                'blood_group' => $this->blood_group ?: null,
                'phone_number' => trim($this->phone_number),
                'present_address' => trim($this->present_address),
                'permanent_address' => trim($this->permanent_address),
                'photo' => $photoPath,
                'occupation' => trim($this->occupation),
                'reference_id' => $this->reference_id ?: null,
                'branch_id' => $this->branch_id,
                'created_by' => Auth::id(),
                'is_active' => true,
            ]);

            if (!$member) {
                throw new \Exception('Failed to create member record in database.');
            }

            \Illuminate\Support\Facades\DB::commit();
            
            // Dispatch events to refresh dashboard stats
            $this->dispatch('memberRegistered', ['member_id' => $member->id]);
            $this->dispatch('refreshMemberCount');
            $this->dispatch('refreshDashboard');
            
            session()->flash('message', 'Member registered successfully! Member ID: ' . $this->generated_member_id);
            $this->resetForm();

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Illuminate\Support\Facades\DB::rollback();
            throw $e; // Re-throw validation exceptions
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\DB::rollback();
            
            // Log the detailed error for easier debugging
            \Illuminate\Support\Facades\Log::error('Member registration failed: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'branch_id' => $this->branch_id,
                'form_data' => [
                    'name' => $this->name,
                    'nid_number' => $this->nid_number,
                    'phone_number' => $this->phone_number,
                    'generated_member_id' => $this->generated_member_id
                ],
                'exception_type' => get_class($e),
                'stack_trace' => $e->getTraceAsString()
            ]);
            
            session()->flash('error', 'Failed to register member: ' . $e->getMessage());
        }
    }

    public function resetForm()
    {
        $this->reset();
        $this->mount();
    }

    public function render()
    {
        return view('livewire.field-officer.member-registration');
    }
}