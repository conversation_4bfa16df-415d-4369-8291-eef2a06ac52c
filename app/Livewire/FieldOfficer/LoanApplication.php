<?php

namespace App\Livewire\FieldOfficer;

use Livewire\Component;
use App\Models\Member;
use App\Models\LoanApplication as LoanApplicationModel;
use App\Models\Branch;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Validate;

class LoanApplication extends Component
{
    #[Validate('required|exists:members,id')]
    public $member_id = '';
    
    #[Validate('required|numeric|min:1000|max:500000')]
    public $applied_amount = '';
    
    #[Validate('required|string|min:10|max:500')]
    public $reason = '';
    
    #[Validate('required|integer|min:1|max:10')]
    public $loan_cycle_number = 1;
    
    #[Validate('required|string|max:255')]
    public $recommender = '';
    
    #[Validate('nullable|numeric|min:0')]
    public $advance_payment = 0;
    
    public $search_term = '';
    public $selected_member = null;
    public $member_suggestions = [];
    public $show_suggestions = false;
    public $show_member_search = false;
    
    // Loan Calculator Properties
    public $total_repayment_amount = 0;
    public $remaining_amount = 0;
    public $installment_count = 12;
    public $installment_amount = 0;
    public $repayment_duration = 12;
    public $repayment_method = 'monthly';
    
    protected $listeners = ['memberSelected'];
    
    public function mount()
    {
        $this->recommender = Auth::user()->name;
    }
    
    public function updatedSearchTerm()
    {
        if (strlen($this->search_term) >= 2) {
            $user = Auth::user();
            $query = Member::where('is_active', true);
            
            // Filter by branch for non-admin users
            if ($user->role !== 'admin' && $user->branch_id) {
                $query->where('branch_id', $user->branch_id);
            }
            
            $this->member_suggestions = $query->where(function($q) {
                $q->where('name', 'like', '%' . $this->search_term . '%')
                  ->orWhere('member_id', 'like', '%' . $this->search_term . '%')
                  ->orWhere('phone_number', 'like', '%' . $this->search_term . '%');
            })
            ->with('branch')
            ->limit(10)
            ->get();
            
            $this->show_suggestions = true;
            $this->show_member_search = true;
        } else {
            $this->member_suggestions = collect();
            $this->show_suggestions = false;
            $this->show_member_search = false;
        }
    }
    
    public function selectMember($memberId)
    {
        $this->member_id = $memberId;
        $this->selected_member = Member::with('branch')->find($memberId);
        
        if ($this->selected_member) {
            $this->search_term = $this->selected_member->name . ' (' . $this->selected_member->member_id . ')';
            $this->show_suggestions = false;
            
            // Get loan cycle number for this member
            $this->loan_cycle_number = LoanApplicationModel::where('member_id', $memberId)->count() + 1;
        }
    }
    
    public function clearMemberSelection()
    {
        $this->member_id = '';
        $this->selected_member = null;
        $this->search_term = '';
        $this->show_suggestions = false;
        $this->loan_cycle_number = 1;
        $this->resetCalculations();
    }
    
    public function updatedAppliedAmount()
    {
        $this->calculateLoan();
    }
    
    public function updatedAdvancePayment()
    {
        $this->calculateLoan();
    }
    
    public function updatedRepaymentDuration()
    {
        $this->installment_count = $this->repayment_duration;
        $this->calculateLoan();
    }
    
    public function calculateLoan()
    {
        if (!$this->applied_amount || $this->applied_amount <= 0) {
            $this->resetCalculations();
            return;
        }
        
        // Calculate total repayment (assuming 20% interest)
        $interest_rate = 0.20;
        $this->total_repayment_amount = $this->applied_amount * (1 + $interest_rate);
        
        // Calculate remaining amount after advance payment
        $advance = $this->advance_payment ?? 0;
        $this->remaining_amount = $this->total_repayment_amount - $advance;
        
        // Calculate installment amount
        if ($this->installment_count > 0) {
            $this->installment_amount = $this->remaining_amount / $this->installment_count;
        }
    }
    
    public function resetCalculations()
    {
        $this->total_repayment_amount = 0;
        $this->remaining_amount = 0;
        $this->installment_amount = 0;
    }
    
    public function submitApplication()
    {
        $this->validate();
        
        if (!$this->selected_member) {
            session()->flash('error', 'Please select a valid member.');
            return;
        }
        
        // Check if member has any pending applications
        $pendingApplication = LoanApplicationModel::where('member_id', $this->member_id)
            ->where('status', 'pending')
            ->exists();
            
        if ($pendingApplication) {
            session()->flash('error', 'This member already has a pending loan application.');
            return;
        }
        
        try {
            LoanApplicationModel::create([
                'member_id' => $this->member_id,
                'applied_amount' => $this->applied_amount,
                'reason' => $this->reason,
                'loan_cycle_number' => $this->loan_cycle_number,
                'recommender' => $this->recommender,
                'advance_payment' => $this->advance_payment ?? 0,
                'status' => 'pending',
                'applied_at' => now(),
            ]);
            
            session()->flash('message', 'Loan application submitted successfully!');
            $this->resetForm();
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Loan application submission failed: ' . $e->getMessage());
            session()->flash('error', 'Failed to submit loan application. Please try again.');
        }
    }
    
    public function resetForm()
    {
        $this->reset([
            'member_id', 'applied_amount', 'reason', 'loan_cycle_number', 
            'advance_payment', 'search_term', 'selected_member', 'member_suggestions'
        ]);
        $this->recommender = Auth::user()->name;
        $this->show_suggestions = false;
        $this->resetCalculations();
    }
    
    public function render()
    {
        return view('livewire.field-officer.loan-application');
    }
}