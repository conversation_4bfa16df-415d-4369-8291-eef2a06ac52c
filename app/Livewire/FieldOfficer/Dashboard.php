<?php

namespace App\Livewire\FieldOfficer;

use Livewire\Component;
use App\Models\Member;
use App\Models\Loan;
use App\Models\Installment;
use App\Models\BranchTransaction;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class Dashboard extends Component
{
    public $totalMembers = 0;
    public $totalCollections = 0;
    public $targetAchievement = 0;
    public $todayActivities = 0;
    public $recentActivities = [];
    public $monthlyTarget = 100000;
    
    protected $listeners = [
        'refreshMemberCount' => 'loadStats', 
        'memberRegistered' => 'loadStats',
        'refreshDashboard' => 'loadStats',
        'installmentCollected' => 'loadStats',
        'loanApplicationSubmitted' => 'loadStats'
    ];
    
    public function mount()
    {
        $this->loadStats();
    }
    
    public function loadStats()
    {
        $user = Auth::user();
        $branchId = $user->branch_id;
        
        if (!$branchId) {
            return;
        }
        
        // Total Members in branch (real-time count)
        $this->totalMembers = Member::where('branch_id', $branchId)
            ->where('is_active', true)
            ->count();
        
        // Total Collections this month (real-time)
        $this->totalCollections = BranchTransaction::where('branch_id', $branchId)
            ->where('transaction_type', 'installment_collection')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('amount');
        
        // Monthly target achievement
        $this->targetAchievement = $this->monthlyTarget > 0 ? round(($this->totalCollections / $this->monthlyTarget) * 100, 1) : 0;
        
        // Today's activities count (real-time)
        $this->todayActivities = BranchTransaction::where('branch_id', $branchId)
            ->where('created_by', $user->id)
            ->whereDate('created_at', today())
            ->count() + 
            Member::where('branch_id', $branchId)
            ->where('created_by', $user->id)
            ->whereDate('created_at', today())
            ->count();
        
        // Load recent activities
        $this->loadRecentActivities();
    }
    
    public function loadRecentActivities()
    {
        $user = Auth::user();
        $branchId = $user->branch_id;
        
        $activities = collect();
        
        // Recent member registrations
        $recentMembers = Member::where('branch_id', $branchId)
            ->where('created_by', $user->id)
            ->latest()
            ->limit(5)
            ->get();
            
        foreach ($recentMembers as $member) {
            $activities->push([
                'description' => "Registered new member: {$member->name} ({$member->member_id})",
                'created_at' => $member->created_at,
                'time' => $member->created_at->diffForHumans(),
                'type' => 'member_registration'
            ]);
        }
        
        // Recent collections
        $recentCollections = BranchTransaction::where('branch_id', $branchId)
            ->where('created_by', $user->id)
            ->where('transaction_type', 'installment_collection')
            ->latest()
            ->limit(5)
            ->get();
            
        foreach ($recentCollections as $collection) {
            // Try to get member name from description or reference
            $memberName = 'Unknown';
            if ($collection->description) {
                // Extract member name from description
                preg_match('/Member: ([^-]+)/', $collection->description, $matches);
                if (isset($matches[1])) {
                    $memberName = trim($matches[1]);
                }
            }
            
            $activities->push([
                'description' => "Collected installment from {$memberName}: ৳" . number_format($collection->amount, 2),
                'created_at' => $collection->created_at,
                'time' => $collection->created_at->diffForHumans(),
                'type' => 'installment_collection'
            ]);
        }
        
        $this->recentActivities = $activities->sortByDesc('created_at')->take(10)->values()->toArray();
    }
    
    public function refreshStats()
    {
        $this->loadStats();
        $this->dispatch('statsRefreshed');
    }
    
    public function refreshData()
    {
        $this->loadStats();
        session()->flash('message', 'Dashboard data refreshed successfully!');
    }
    
    public function render()
    {
        return view('livewire.field-officer.dashboard', [
            'totalMembers' => $this->totalMembers,
            'totalCollections' => $this->totalCollections,
            'targetAchievement' => $this->targetAchievement,
            'todayActivities' => $this->todayActivities,
            'recentActivities' => $this->recentActivities
        ]);
    }
}
