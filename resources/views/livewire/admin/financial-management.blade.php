<div class="p-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Financial Management</h2>
            <p class="text-gray-600">Monitor income, expenses, and financial performance across all branches</p>
            </div>

            <div class="flex space-x-2 mt-4 sm:mt-0">
            <x-button wire:click="openTransactionModal" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                <span>Add Transaction</span>
            </x-button>
            <x-button wire:click="exportFinancialReport" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span>Export Report</span>
            </x-button>
        </div>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {{ session('message') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {{ session('error') }}
        </div>
    @endif

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <x-forms.label value="Branch" />
                <x-forms.select wire:model.live="selectedBranch" class="w-full">
                    <option value="all">All Branches</option>
                    @foreach($branches as $branch)
                        <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <x-forms.label value="From Date" />
                <x-forms.input wire:model.live="dateFrom" type="date" class="w-full" />
            </div>
            <div>
                <x-forms.label value="To Date" />
                <x-forms.input wire:model.live="dateTo" type="date" class="w-full" />
            </div>
            <div>
                <x-forms.label value="Transaction Type" />
                <x-forms.select wire:model.live="transactionType" class="w-full">
                    <option value="all">All Types</option>
                    <option value="income">Income</option>
                    <option value="expense">Expense</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Financial Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>


                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Loans</p>
                    <p class="text-2xl font-bold text-gray-900">৳{{ number_format($financialOverview['total_loans'] ?? 0) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 8h6m-5 0a3 3 0 110 6H9l3 3-3-3zm1 3h6m-6 0a3 3 0 110-6H9l3-3-3 3z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Collections</p>
                    <p class="text-2xl font-bold text-gray-900">৳{{ number_format($financialOverview['total_collections'] ?? 0) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Net Income</p>
                    <p class="text-2xl font-bold {{ ($financialOverview['net_income'] ?? 0) >= 0 ? 'text-green-600' : 'text-red-600' }}">
                        ৳{{ number_format($financialOverview['net_income'] ?? 0) }}
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Outstanding</p>
                    <p class="text-2xl font-bold text-gray-900">৳{{ number_format($financialOverview['total_outstanding'] ?? 0) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Financial Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Income vs Expense</h3>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-green-600">Income:</span>
                    <span class="font-semibold text-green-600">৳{{ number_format($financialOverview['total_income'] ?? 0) }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-red-600">Expense:</span>
                    <span class="font-semibold text-red-600">৳{{ number_format($financialOverview['total_expense'] ?? 0) }}</span>
                </div>
                <hr>
                <div class="flex justify-between">
                    <span class="font-semibold">Net:</span>
                    <span class="font-bold {{ ($financialOverview['net_income'] ?? 0) >= 0 ? 'text-green-600' : 'text-red-600' }}">
                        ৳{{ number_format($financialOverview['net_income'] ?? 0) }}
                    </span>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Collection Performance</h3>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span>Collection Rate:</span>
                    <span class="font-semibold">{{ number_format($financialOverview['collection_rate'] ?? 0, 2) }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-600 h-2 rounded-full" style="width: {{ number_format(min(100, $financialOverview['collection_rate'] ?? 0), 2) }}%"></div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Overdue Status</h3>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-red-600">Overdue Amount:</span>
                    <span class="font-semibold text-red-600">৳{{ number_format($financialOverview['total_overdue'] ?? 0) }}</span>
                </div>
                <div class="flex justify-between">
                    <span>Overdue Rate:</span>
                    <span class="font-semibold">{{ ($financialOverview['total_outstanding'] ?? 0) > 0 ? number_format((($financialOverview['total_overdue'] ?? 0) / ($financialOverview['total_outstanding'] ?? 1)) * 100, 2) : 0 }}%</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Branch Financial Performance -->
    @if(count($branchFinancials) > 0)
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Branch Financial Performance</h3>
            </div>
            <div class="overflow-x-auto">
                <x-ui.data-table>
                    <x-ui.table-head>
                        <tr>
                            <x-ui.table-header>Branch</x-ui.table-header>
                            <x-ui.table-header>Loans</x-ui.table-header>
                            <x-ui.table-header>Collections</x-ui.table-header>
                            <x-ui.table-header>Income</x-ui.table-header>
                            <x-ui.table-header>Expense</x-ui.table-header>
                            <x-ui.table-header>Net Income</x-ui.table-header>
                            <x-ui.table-header>Outstanding</x-ui.table-header>
                        </tr>
                    </x-ui.table-head>
                    <x-ui.table-body>
                        @foreach($branchFinancials as $branch)
                            <x-ui.table-row>
                                 <x-ui.table-column>৳{{ number_format($branch['total_loans'] ?? 0) }}</x-ui.table-column>
                                 <x-ui.table-column>৳{{ number_format($branch['total_collections'] ?? 0) }}</x-ui.table-column>
                                 <x-ui.table-column>৳{{ number_format($branch['total_income'] ?? 0) }}</x-ui.table-column>
                                 <x-ui.table-column>৳{{ number_format($branch['total_expense'] ?? 0) }}</x-ui.table-column>
                                 <x-ui.table-column class="{{ ($branch['net_income'] ?? 0) >= 0 ? 'text-green-600' : 'text-red-600' }}">৳{{ number_format($branch['net_income'] ?? 0) }}</x-ui.table-column>
                                 <x-ui.table-column>৳{{ number_format($branch['total_outstanding'] ?? 0) }}</x-ui.table-column>
                             </x-ui.table-row>
                         @endforeach
                     </x-ui.table-body>
                 </x-ui.data-table>
             </div>
         </div>
    @endif




    <!-- Monthly Trends Chart -->
    @if(count($monthlyTrends) > 0)
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Monthly Financial Trends</h3>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b">
                                <th class="text-left py-2">Month</th>
                                <th class="text-right py-2">Loans</th>
                                <th class="text-right py-2">Collections</th>
                                <th class="text-right py-2">Income</th>
                                <th class="text-right py-2">Expense</th>
                                <th class="text-right py-2">Net Income</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($monthlyTrends as $trend)
                                <tr class="border-b hover:bg-gray-50">
                                    <td class="py-2 font-medium">{{ $trend['month'] }}</td>
                                    <td class="py-2 text-right">৳{{ number_format($trend['loans']) }}</td>
                                    <td class="py-2 text-right">৳{{ number_format($trend['collections']) }}</td>
                                    <td class="py-2 text-right text-green-600">৳{{ number_format($trend['income']) }}</td>
                                    <td class="py-2 text-right text-red-600">৳{{ number_format($trend['expense']) }}</td>
                                    <td class="py-2 text-right font-medium {{ $trend['net_income'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                        ৳{{ number_format($trend['net_income']) }}
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                 </div>
             </div>
         </div>
    @endif
    @endif

    <!-- Recent Transactions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">Recent Transactions</h3>
                <input wire:model.live="search" type="text" placeholder="Search transactions..." 
                       class="px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
        </div>
        <div class="overflow-x-auto">
            <x-ui.data-table>
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($transactions as $transaction)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $transaction->transaction_date->format('M d, Y') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $transaction->branch->name }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $transaction->transaction_type === 'income' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ ucfirst($transaction->transaction_type) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                {{ Str::limit($transaction->description, 50) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium {{ $transaction->transaction_type === 'income' ? 'text-green-600' : 'text-red-600' }}">
                                ৳{{ number_format($transaction->amount) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $transaction->creator->name ?? 'System' }}
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                No transactions found.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="px-6 py-3 border-t border-gray-200">
            {{ $transactions->links() }}
        </div>
    </div>

    <!-- Add Transaction Modal -->
    <x-ui.modal wire:model="showTransactionModal" close-on-escape="true" max-width="2xl">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Add New Transaction</h3>
            
            <form wire:submit.prevent="addTransaction">
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Branch *</label>
                        <select wire:model="transactionBranch" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Select Branch</option>
                            @foreach($branches as $branch)
                                <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                            @endforeach
                        </select>
                        @error('transactionBranch') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Transaction Type *</label>
                        <select wire:model="transactionTypeModal" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="income">Income</option>
                            <option value="expense">Expense</option>
                        </select>
                        @error('transactionTypeModal') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Amount *</label>
                        <input wire:model="transactionAmount" type="number" step="0.01" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        @error('transactionAmount') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Description *</label>
                        <textarea wire:model="transactionDescription" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        @error('transactionDescription') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6">
                    <x-secondary-button type="button" wire:click="closeTransactionModal" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        Cancel
                    </x-secondary-button>
                    <x-button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Add Transaction
                    </x-button>
                </div>
            </form>
        </div>
    </x-ui.modal>
</div>