<div class="max-w-4xl mx-auto p-4 space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Member Registration</h2>
                <p class="text-gray-600 mt-1">Register a new member to the system</p>
            </div>
            <button wire:click="resetForm" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg transition-colors">
                <x-heroicon-o-arrow-path class="w-5 h-5 inline mr-2" />
                Reset Form
            </button>
        </div>
    </div>

    <!-- Registration Form -->
    <form wire:submit.prevent="createMember" class="space-y-6">
        <!-- Personal Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <x-heroicon-o-user class="w-5 h-5 mr-2 text-blue-600" />
                Personal Information
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Member Name -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                    <input type="text" wire:model="name" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror"
                           placeholder="Enter full name">
                    @error('name') <p class="text-red-500 text-sm mt-1">{{ $message }}</p> @enderror
                </div>

                <!-- Father/Husband Name -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Father/Husband Name *</label>
                    <input type="text" wire:model="father_or_husband_name" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('father_or_husband_name') border-red-500 @enderror"
                           placeholder="Enter father or husband name">
                    @error('father_or_husband_name') <p class="text-red-500 text-sm mt-1">{{ $message }}</p> @enderror
                </div>

                <!-- Mother Name -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Mother Name *</label>
                    <input type="text" wire:model="mother_name" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('mother_name') border-red-500 @enderror"
                           placeholder="Enter mother name">
                    @error('mother_name') <p class="text-red-500 text-sm mt-1">{{ $message }}</p> @enderror
                </div>

                <!-- Date of Birth -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Date of Birth *</label>
                    <input type="date" wire:model="date_of_birth" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('date_of_birth') border-red-500 @enderror">
                    @error('date_of_birth') <p class="text-red-500 text-sm mt-1">{{ $message }}</p> @enderror
                </div>

                <!-- NID Number -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">NID Number *</label>
                    <input type="text" wire:model="nid_number" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('nid_number') border-red-500 @enderror"
                           placeholder="Enter NID number">
                    @error('nid_number') <p class="text-red-500 text-sm mt-1">{{ $message }}</p> @enderror
                </div>

                <!-- Religion -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Religion</label>
                    <select wire:model="religion" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select Religion</option>
                        <option value="Islam">Islam</option>
                        <option value="Hinduism">Hinduism</option>
                        <option value="Christianity">Christianity</option>
                        <option value="Buddhism">Buddhism</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                <!-- Blood Group -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Blood Group</label>
                    <select wire:model="blood_group" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select Blood Group</option>
                        <option value="A+">A+</option>
                        <option value="A-">A-</option>
                        <option value="B+">B+</option>
                        <option value="B-">B-</option>
                        <option value="AB+">AB+</option>
                        <option value="AB-">AB-</option>
                        <option value="O+">O+</option>
                        <option value="O-">O-</option>
                    </select>
                </div>

                <!-- Phone Number -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                    <input type="tel" wire:model="phone_number" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('phone_number') border-red-500 @enderror"
                           placeholder="Enter phone number">
                    @error('phone_number') <p class="text-red-500 text-sm mt-1">{{ $message }}</p> @enderror
                </div>
            </div>
        </div>

        <!-- Address Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <x-heroicon-o-map-pin class="w-5 h-5 mr-2 text-green-600" />
                Address Information
            </h3>
            
            <div class="space-y-4">
                <!-- Present Address -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Present Address *</label>
                    <textarea wire:model="present_address" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('present_address') border-red-500 @enderror"
                              placeholder="Enter present address"></textarea>
                    @error('present_address') <p class="text-red-500 text-sm mt-1">{{ $message }}</p> @enderror
                </div>

                <!-- Same as Present Address Checkbox -->
                <div class="flex items-center">
                    <input type="checkbox" wire:model="same_as_present" wire:click="toggleSameAddress" 
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label class="ml-2 text-sm text-gray-700">Permanent address same as present address</label>
                </div>

                <!-- Permanent Address -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Permanent Address *</label>
                    <textarea wire:model="permanent_address" rows="3" {{ $same_as_present ? 'disabled' : '' }}
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('permanent_address') border-red-500 @enderror {{ $same_as_present ? 'bg-gray-100' : '' }}"
                              placeholder="Enter permanent address"></textarea>
                    @error('permanent_address') <p class="text-red-500 text-sm mt-1">{{ $message }}</p> @enderror
                </div>
            </div>
        </div>

        <!-- Photo Upload -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <x-heroicon-o-camera class="w-5 h-5 mr-2 text-purple-600" />
                Photo Upload
            </h3>
            
            <div class="flex flex-col md:flex-row gap-6">
                <!-- Photo Preview -->
                <div class="flex-shrink-0">
                    @if ($photo_preview)
                        <img src="{{ $photo_preview }}" alt="Photo Preview" 
                             class="w-32 h-32 object-cover rounded-lg border-2 border-gray-300">
                    @else
                        <div class="w-32 h-32 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                            <x-heroicon-o-user class="w-12 h-12 text-gray-400" />
                        </div>
                    @endif
                </div>
                
                <!-- Upload Controls -->
                <div class="flex-1">
                    <input type="file" wire:model="photo" accept="image/*" 
                           class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                    @error('photo') <p class="text-red-500 text-sm mt-1">{{ $message }}</p> @enderror
                    <p class="text-sm text-gray-500 mt-2">Upload a clear photo (JPG, PNG, max 2MB)</p>
                    
                    @if ($photo_preview)
                        <button type="button" wire:click="removePhoto" 
                                class="mt-2 text-red-600 hover:text-red-800 text-sm font-medium">
                            Remove Photo
                        </button>
                    @endif
                </div>
            </div>
        </div>

        <!-- Professional Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <x-heroicon-o-briefcase class="w-5 h-5 mr-2 text-orange-600" />
                Professional Information
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Occupation -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Occupation *</label>
                    <input type="text" wire:model="occupation" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('occupation') border-red-500 @enderror"
                           placeholder="Enter occupation">
                    @error('occupation') <p class="text-red-500 text-sm mt-1">{{ $message }}</p> @enderror
                </div>

                <!-- Reference -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Reference</label>
                    <select wire:model="reference_id" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select Reference</option>
                        @foreach($references as $reference)
                            <option value="{{ $reference->id }}">{{ $reference->name }} ({{ $reference->member_id }})</option>
                        @endforeach
                    </select>
                </div>

                <!-- Branch -->
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Branch *</label>
                    @if(Auth::user()->role !== 'admin' && Auth::user()->branch_id)
                        <input type="text" value="{{ $branches->where('id', $branch_id)->first()->name ?? 'N/A' }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 cursor-not-allowed" readonly>
                        <input type="hidden" wire:model="branch_id">
                    @else
                        <select wire:model.live="branch_id" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('branch_id') border-red-500 @enderror">
                            <option value="">Select Branch</option>
                            @foreach($branches as $branch)
                                <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                            @endforeach
                        </select>
                    @endif
                    @error('branch_id') <p class="text-red-500 text-sm mt-1">{{ $message }}</p> @enderror
                </div>
            </div>
        </div>

        <!-- Generated Member ID Display -->
        @if($generated_member_id)
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center">
                    <x-heroicon-o-identification class="w-5 h-5 text-blue-600 mr-2" />
                    <span class="text-sm font-medium text-blue-800">Generated Member ID: </span>
                    <span class="text-lg font-bold text-blue-900 ml-2">{{ $generated_member_id }}</span>
                </div>
            </div>
        @endif

        <!-- Form Actions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex flex-col sm:flex-row gap-4 justify-end">
                <button type="button" wire:click="resetForm" 
                        class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Reset Form
                </button>
                <button type="submit" 
                        class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                        wire:loading.attr="disabled">
                    <span wire:loading.remove>Register Member</span>
                    <span wire:loading class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                    </span>
                </button>
            </div>
        </div>
    </form>

    <!-- Success/Error Messages -->
    @if (session()->has('message'))
        <div class="fixed top-4 right-4 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg" 
             x-data="{ show: true }" x-show="show" x-init="setTimeout(() => show = false, 5000)">
            <div class="flex items-center">
                <x-heroicon-o-check-circle class="w-5 h-5 mr-2" />
                {{ session('message') }}
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="fixed top-4 right-4 z-50 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg" 
             x-data="{ show: true }" x-show="show" x-init="setTimeout(() => show = false, 5000)">
            <div class="flex items-center">
                <x-heroicon-o-x-circle class="w-5 h-5 mr-2" />
                {{ session('error') }}
            </div>
        </div>
    @endif
</div>
